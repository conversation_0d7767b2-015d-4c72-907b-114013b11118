%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a7d75bea1662418ab5f9e0c22110bc09, type: 3}
  m_Name: ImmersiveDebuggerSettings
  m_EditorClassIdentifier: 
  debugTypes: []
  immersiveDebuggerEnabled: 0
  immersiveDebuggerDisplayAtStartup: 0
  showInspectors: 0
  showConsole: 0
  followOverride: 1
  rotateOverride: 0
  showInfoLog: 0
  showWarningLog: 1
  showErrorLog: 1
  collapsedIdenticalLogEntries: 0
  maximumNumberOfLogEntries: 1000
  panelDistance: 1
  createEventSystem: 1
  automaticLayerCullingUpdate: 1
  panelLayer: 20
  meshRendererLayer: 21
  overlayDepth: 10
  useOverlay: 1
  inspectedDataEnabled: 
  inspectedDataAssets: []
  useCustomIntegrationConfig: 0
  customIntegrationConfigClassName: 
  hierarchyViewShowsPrivateMembers: 0
  clickButton: 8193
  toggleFollowTranslationButton: 0
  toggleFollowRotationButton: 0
  immersiveDebuggerToggleDisplayButton: 2
