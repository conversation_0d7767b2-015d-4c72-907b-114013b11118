{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}, {"build": "GameActivity", "jsonFile": "directory-GameActivity-RelWithDebInfo-3edf2ef9e25ca0503118.json", "minimumCMakeVersion": {"string": "3.4.1"}, "parentIndex": 0, "projectIndex": 1, "source": "GameActivity", "targetIndexes": [0]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "Unity"}, {"directoryIndexes": [1], "name": "game", "parentIndex": 0, "targetIndexes": [0]}], "targets": [{"directoryIndex": 1, "id": "game::@d02bb112ea9f9c2ed29f", "jsonFile": "target-game-RelWithDebInfo-c9dd5a7ebb4d35384724.json", "name": "game", "projectIndex": 1}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Downloads/toy_forge-main/toy_forge-main/.utmp/RelWithDebInfo/1mj1r622/arm64-v8a", "source": "C:/Users/<USER>/Downloads/toy_forge-main/toy_forge-main/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp"}, "version": {"major": 2, "minor": 3}}