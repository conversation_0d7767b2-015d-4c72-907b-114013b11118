%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9052863463845294752
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature WebGL
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-8420637975197410255
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f928d0d73a35f294fbe357ca17aa3547, type: 3}
  m_Name: MicrosoftHandInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Microsoft Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handtracking
  openxrExtensionStrings: XR_MSFT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7654847616871054648
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 056125dd64c0ed540b40a4af74f7b495, type: 3}
  m_Name: RuntimeDebuggerOpenXRFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Runtime Debugger
  version: 1
  featureIdInternal: com.unity.openxr.features.runtimedebugger
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
  cacheSize: 1048576
  perThreadCacheSize: 51200
--- !u!114 &-7242700116014674625
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7de993716e042c6499d0c18eed3a773c, type: 3}
  m_Name: MockRuntime Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Mock Runtime
  version: 0.0.2
  featureIdInternal: com.unity.openxr.feature.mockruntime
  openxrExtensionStrings: XR_UNITY_null_gfx XR_UNITY_android_present
  company: Unity
  priority: 0
  required: 0
  ignoreValidationErrors: 0
--- !u!114 &-6922328838823630454
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f928d0d73a35f294fbe357ca17aa3547, type: 3}
  m_Name: MicrosoftHandInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Microsoft Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handtracking
  openxrExtensionStrings: XR_MSFT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-6884480219883586423
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 274c02963f889a64e90bc2e596e21d13, type: 3}
  m_Name: HTCViveControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: HTC Vive Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.htcvive
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-6773624625965287446
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 486b5e28864f9a94b979b9620ce5006d, type: 3}
  m_Name: ConformanceAutomationFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Conformance Automation
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.conformance
  openxrExtensionStrings: XR_EXT_conformance_automation
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-6551355169549751462
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5019471fb2174e5c852ecd4047163007, type: 3}
  m_Name: HandInteractionProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteraction
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-5374270425685486978
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f6a75d1f5ff90154ea2a8e58222a1f59, type: 3}
  m_Name: FoveatedRenderingFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Foveated Rendering
  version: 1
  featureIdInternal: com.unity.openxr.feature.foveatedrendering
  openxrExtensionStrings: XR_UNITY_foveation XR_FB_foveation XR_FB_foveation_configuration
    XR_FB_swapchain_update_state XR_FB_foveation_vulkan XR_META_foveation_eye_tracked
    XR_META_vulkan_swapchain_create_info
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-5292101578597056669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c5b5af5107e35a43818d5411328bfc3, type: 3}
  m_Name: DPadInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: D-Pad Binding
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.dpadinteraction
  openxrExtensionStrings: XR_KHR_binding_modification XR_EXT_dpad_binding
  company: Unity
  priority: 0
  required: 0
  forceThresholdLeft: 0.5
  forceThresholdReleaseLeft: 0.4
  centerRegionLeft: 0.5
  wedgeAngleLeft: 1.5707964
  isStickyLeft: 0
  forceThresholdRight: 0.5
  forceThresholdReleaseRight: 0.4
  centerRegionRight: 0.5
  wedgeAngleRight: 1.5707964
  isStickyRight: 0
  extensionStrings:
  - XR_KHR_binding_modification
  - XR_EXT_dpad_binding
--- !u!114 &-5284127780566887237
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b7365b139f7aec43b23d26b7a48b5a6, type: 3}
  m_Name: MetaQuestTouchPlusControllerProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Plus Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestplus
  openxrExtensionStrings: XR_META_touch_controller_plus
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-3124934033516091095
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 056125dd64c0ed540b40a4af74f7b495, type: 3}
  m_Name: RuntimeDebuggerOpenXRFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Runtime Debugger
  version: 1
  featureIdInternal: com.unity.openxr.features.runtimedebugger
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
  cacheSize: 1048576
  perThreadCacheSize: 51200
--- !u!114 &-3105453437923318131
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: Standalone
  m_EditorClassIdentifier: 
  features:
  - {fileID: -6773624625965287446}
  - {fileID: 8394232375306015077}
  - {fileID: -******************}
  - {fileID: 7613218367301664307}
  - {fileID: 8805067575581680119}
  - {fileID: -6551355169549751462}
  - {fileID: 1871588430529899246}
  - {fileID: -6884480219883586423}
  - {fileID: 501767589206182809}
  - {fileID: 6839507326734470399}
  - {fileID: -327143109823431642}
  - {fileID: 1149626986402068852}
  - {fileID: -1658252386213997653}
  - {fileID: 444882*************}
  - {fileID: 8480006813584753048}
  - {fileID: -6922328838823630454}
  - {fileID: 6888332146621913804}
  - {fileID: -7242700116014674625}
  - {fileID: -2315189427254313582}
  - {fileID: 8965433883820857904}
  - {fileID: -7654847616871054648}
  - {fileID: 6196356933332250687}
  - {fileID: -2718318735185046268}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_spacewarpMotionVectorTextureFormat: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
  m_optimizeMultiviewRenderRegions: 0
  m_foveatedRenderingApi: 0
--- !u!114 &-2732368499598533186
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81954520b8bbd2f458104fe69f5a7680, type: 3}
  m_Name: MetaXRSpaceWarp Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta XR Space Warp
  version: 1.0.0
  featureIdInternal: com.meta.openxr.feature.spacewarp
  openxrExtensionStrings: XR_FB_space_warp
  company: Meta
  priority: 0
  required: 0
--- !u!114 &-2718318735185046268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-2315189427254313582
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: feeef8d85de8db242bdda70cc7ff5acd, type: 3}
  m_Name: OculusTouchControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 1
  nameUi: Oculus Touch Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.oculustouch
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-1658252386213997653
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1927c045052a06d49a9b21fdcaa26db6, type: 3}
  m_Name: MetaXRFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 1
  nameUi: Meta XR Feature
  version: 0.0.1
  featureIdInternal: com.meta.openxr.feature.metaxr
  openxrExtensionStrings: 'XR_KHR_vulkan_enable XR_KHR_D3D11_enable XR_OCULUS_common_reference_spaces
    XR_FB_display_refresh_rate XR_EXT_performance_settings XR_FB_composition_layer_image_layout
    XR_KHR_android_surface_swapchain XR_FB_android_surface_swapchain_create XR_KHR_composition_layer_color_scale_bias
    XR_FB_color_space XR_EXT_hand_tracking XR_FB_swapchain_update_state XR_FB_swapchain_update_state_opengl_es
    XR_FB_swapchain_update_state_vulkan XR_FB_composition_layer_alpha_blend XR_KHR_composition_layer_depth
    XR_KHR_composition_layer_cylinder XR_KHR_composition_layer_cube XR_KHR_composition_layer_equirect2
    XR_KHR_convert_timespec_time XR_KHR_visibility_mask XR_FB_render_model XR_FB_spatial_entity
    XR_FB_spatial_entity_user XR_FB_spatial_entity_query XR_FB_spatial_entity_storage
    XR_FB_spatial_entity_storage_batch XR_META_spatial_entity_mesh XR_META_performance_metrics
    XR_FB_spatial_entity_sharing XR_FB_scene XR_FB_spatial_entity_container XR_FB_scene_capture
    XR_FB_face_tracking XR_FB_face_tracking2 XR_FB_eye_tracking XR_FB_eye_tracking_social
    XR_FB_body_tracking XR_META_body_tracking_full_body XR_META_body_tracking_calibration
    XR_META_body_tracking_fidelity XR_FB_keyboard_tracking XR_META_virtual_keyboard
    XR_FB_passthrough XR_FB_triangle_mesh XR_FB_passthrough_keyboard_hands XR_META_passthrough_layer_resumed_event
    XR_META_passthrough_color_lut XR_META_passthrough_preferences XR_OCULUS_audio_device_guid
    XR_FB_common_events XR_FB_hand_tracking_capsules XR_FB_hand_tracking_mesh XR_FB_hand_tracking_aim
    XR_FB_touch_controller_pro XR_FB_touch_controller_proximity XR_FB_composition_layer_depth_test
    XR_FB_haptic_amplitude_envelope XR_FB_haptic_pcm XR_META_local_dimming XR_META_hand_tracking_wide_motion_mode
    XR_EXT_hand_tracking_data_source XR_EXT_hand_joints_motion_range XR_META_touch_controller_plus
    XR_META_simultaneous_hands_and_controllers XR_MSFT_hand_interaction XR_EXT_hand_interaction
    XR_FB_hand_tracking_confidence XR_META_detached_controllers XR_LOGITECH_mx_ink_stylus_interaction
    XR_META_colocation_discovery XR_META_spatial_entity_sharing XR_META_spatial_entity_group_sharing
    XR_EXT_debug_utils XR_META_dynamic_object_tracker XR_META_dynamic_object_keyboard
    XR_META_hand_tracking_microgestures XR_META_spatial_entity_persistence XR_META_spatial_entity_discovery
    XR_META_boundary_visibility XR_METAX1_face_tracking_visemes XR_META_headset_id
    XR_FB_composition_layer_settings XR_META_automatic_layer_filter XR_EXT_future
    XR_META_recommended_layer_resolution XR_EXT_local_floor '
  company: Meta
  priority: 0
  required: 0
--- !u!114 &-1136239057054046491
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e573a2aed6698b047aff95224499b722, type: 3}
  m_Name: MetaXREyeTrackedFoveationFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta XR Eye Tracked Foveation
  version: 0.0.1
  featureIdInternal: com.meta.openxr.feature.eyetrackedfoveation
  openxrExtensionStrings: XR_META_foveation_eye_tracked XR_FB_eye_tracking_social
    XR_META_vulkan_swapchain_create_info
  company: Meta
  priority: 0
  required: 0
--- !u!114 &-738632431524255697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: Android
  m_EditorClassIdentifier: 
  features:
  - {fileID: 5939582768225331391}
  - {fileID: -5292101578597056669}
  - {fileID: 2806408499831315055}
  - {fileID: -5374270425685486978}
  - {fileID: 3986283775265371435}
  - {fileID: 945292431750053072}
  - {fileID: 8641999004521932936}
  - {fileID: 597280758957623306}
  - {fileID: -5284127780566887237}
  - {fileID: 7440647862011881471}
  - {fileID: -1136239057054046491}
  - {fileID: 9038805937758088593}
  - {fileID: 178481023206387585}
  - {fileID: -2732368499598533186}
  - {fileID: -649065013312154807}
  - {fileID: -8420637975197410255}
  - {fileID: 950510729869340638}
  - {fileID: 3258119114710503064}
  - {fileID: *******************}
  - {fileID: *******************}
  - {fileID: -3124934033516091095}
  - {fileID: 3530218945284833453}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_spacewarpMotionVectorTextureFormat: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
  m_optimizeMultiviewRenderRegions: 0
  m_foveatedRenderingApi: 0
--- !u!114 &-649065013312154807
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4710975c7840e7d499bbb3757c22b0a5, type: 3}
  m_Name: MetaXRSubsampledLayout Android
  m_EditorClassIdentifier: 
  m_enabled: 1
  nameUi: Meta XR Subsampled Layout
  version: 0.0.1
  featureIdInternal: com.meta.openxr.feature.subsampledLayout
  openxrExtensionStrings: XR_META_vulkan_swapchain_create_info
  company: Meta
  priority: 0
  required: 0
--- !u!114 &-******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3cf79659a011bd419c7a2a30eb74e9a, type: 3}
  m_Name: EyeGazeInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Eye Gaze Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.eyetracking
  openxrExtensionStrings: XR_EXT_eye_gaze_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-327143109823431642
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4b862ee14fb479fbfe5fffe655d3ed3, type: 3}
  m_Name: MetaQuestTouchProControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Pro Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestpro
  openxrExtensionStrings: XR_FB_touch_controller_pro
  company: Unity
  priority: 0
  required: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f0ebc320a151d3408ea1e9fce54d40e, type: 3}
  m_Name: OpenXR Package Settings
  m_EditorClassIdentifier: 
  Keys: 01000000070000000d000000
  Values:
  - {fileID: -3105453437923318131}
  - {fileID: -738632431524255697}
  - {fileID: 8676458532043711687}
--- !u!114 &178481023206387585
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d28f705476c80d47acb3dfade3d3142, type: 3}
  m_Name: MetaXRFoveationFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 1
  nameUi: Meta XR Foveation
  version: 1.0.0
  featureIdInternal: com.meta.openxr.feature.foveation
  openxrExtensionStrings: 'XR_FB_foveation XR_FB_foveation_configuration XR_FB_foveation_vulkan '
  company: Meta
  priority: 0
  required: 0
--- !u!114 &501767589206182809
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f6bfdbcb316ed242b30a8798c9eb853, type: 3}
  m_Name: KHRSimpleControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Khronos Simple Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.khrsimpleprofile
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &597280758957623306
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f647cc0545697264a9878224faada6d5, type: 3}
  m_Name: MetaQuestFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Support
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.metaquest
  openxrExtensionStrings: XR_OCULUS_android_initialize_loader
  company: Unity
  priority: 0
  required: 0
  targetDevices:
  - visibleName: Quest
    manifestName: quest
    enabled: 1
  - visibleName: Quest 2
    manifestName: quest2
    enabled: 1
  - visibleName: Quest Pro
    manifestName: cambria
    enabled: 1
  - visibleName: Quest 3
    manifestName: eureka
    enabled: 1
  - visibleName: Quest 3S
    manifestName: quest3s
    enabled: 1
  forceRemoveInternetPermission: 0
  symmetricProjection: 0
  foveatedRenderingApi: 0
  systemSplashScreen: {fileID: 0}
  optimizeBufferDiscards: 1
  lateLatchingMode: 0
  lateLatchingDebug: 0
  optimizeMultiviewRenderRegions: 0
  spacewarpMotionVectorTextureFormat: 0
--- !u!114 &945292431750053072
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5019471fb2174e5c852ecd4047163007, type: 3}
  m_Name: HandInteractionProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteraction
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &950510729869340638
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7de993716e042c6499d0c18eed3a773c, type: 3}
  m_Name: MockRuntime Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Mock Runtime
  version: 0.0.2
  featureIdInternal: com.unity.openxr.feature.mockruntime
  openxrExtensionStrings: XR_UNITY_null_gfx XR_UNITY_android_present
  company: Unity
  priority: 0
  required: 0
  ignoreValidationErrors: 0
--- !u!114 &1149626986402068852
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e573a2aed6698b047aff95224499b722, type: 3}
  m_Name: MetaXREyeTrackedFoveationFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta XR Eye Tracked Foveation
  version: 0.0.1
  featureIdInternal: com.meta.openxr.feature.eyetrackedfoveation
  openxrExtensionStrings: XR_META_foveation_eye_tracked XR_FB_eye_tracking_social
    XR_META_vulkan_swapchain_create_info
  company: Meta
  priority: 0
  required: 0
--- !u!114 &1871588430529899246
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e5315f812f023cf4ebf26f7e5d2d70f2, type: 3}
  m_Name: HPReverbG2ControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: HP Reverb G2 Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.hpreverb
  openxrExtensionStrings: XR_EXT_hp_mixed_reality_controller
  company: Unity
  priority: 0
  required: 0
--- !u!114 &2806408499831315055
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3cf79659a011bd419c7a2a30eb74e9a, type: 3}
  m_Name: EyeGazeInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Eye Gaze Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.eyetracking
  openxrExtensionStrings: XR_EXT_eye_gaze_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &3258119114710503064
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9ef793c31862a37448e907829482ef80, type: 3}
  m_Name: OculusQuestFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Oculus Quest Support
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.oculusquest
  openxrExtensionStrings: XR_OCULUS_android_initialize_loader
  company: Unity
  priority: 0
  required: 0
  targetQuest: 1
  targetQuest2: 1
--- !u!114 &3530218945284833453
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &3986283775265371435
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a24be4b5ebfe5f4d8ed1de9b25cb7aa, type: 3}
  m_Name: HandCommonPosesInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Hand Interaction Poses
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteractionposes
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &444882*************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d28f705476c80d47acb3dfade3d3142, type: 3}
  m_Name: MetaXRFoveationFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 1
  nameUi: Meta XR Foveation
  version: 1.0.0
  featureIdInternal: com.meta.openxr.feature.foveation
  openxrExtensionStrings: 'XR_FB_foveation XR_FB_foveation_configuration XR_FB_foveation_vulkan '
  company: Meta
  priority: 0
  required: 0
--- !u!114 &*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f028123e2efe1d443875bc7609b4a98b, type: 3}
  m_Name: PalmPoseInteraction Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Palm Pose
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.palmpose
  openxrExtensionStrings: XR_EXT_palm_pose
  company: Unity
  priority: 0
  required: 0
--- !u!114 &*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: feeef8d85de8db242bdda70cc7ff5acd, type: 3}
  m_Name: OculusTouchControllerProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 1
  nameUi: Oculus Touch Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.oculustouch
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &5939582768225331391
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 486b5e28864f9a94b979b9620ce5006d, type: 3}
  m_Name: ConformanceAutomationFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Conformance Automation
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.conformance
  openxrExtensionStrings: XR_EXT_conformance_automation
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6196356933332250687
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d6ccd3d0ef0f1d458e69421dccbdae1, type: 3}
  m_Name: ValveIndexControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Valve Index Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.valveindex
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6839507326734470399
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b7365b139f7aec43b23d26b7a48b5a6, type: 3}
  m_Name: MetaQuestTouchPlusControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Plus Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestplus
  openxrExtensionStrings: XR_META_touch_controller_plus
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6888332146621913804
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 761fdd4502cb7a84e9ec7a2b24f33f37, type: 3}
  m_Name: MicrosoftMotionControllerProfile Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Microsoft Motion Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.microsoftmotioncontroller
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &7440647862011881471
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4b862ee14fb479fbfe5fffe655d3ed3, type: 3}
  m_Name: MetaQuestTouchProControllerProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta Quest Touch Pro Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestpro
  openxrExtensionStrings: XR_FB_touch_controller_pro
  company: Unity
  priority: 0
  required: 0
--- !u!114 &7613218367301664307
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f6a75d1f5ff90154ea2a8e58222a1f59, type: 3}
  m_Name: FoveatedRenderingFeature Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Foveated Rendering
  version: 1
  featureIdInternal: com.unity.openxr.feature.foveatedrendering
  openxrExtensionStrings: XR_UNITY_foveation XR_FB_foveation XR_FB_foveation_configuration
    XR_FB_swapchain_update_state XR_FB_foveation_vulkan XR_META_foveation_eye_tracked
    XR_META_vulkan_swapchain_create_info
  company: Unity
  priority: 0
  required: 0
--- !u!114 &8394232375306015077
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c5b5af5107e35a43818d5411328bfc3, type: 3}
  m_Name: DPadInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: D-Pad Binding
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.dpadinteraction
  openxrExtensionStrings: XR_KHR_binding_modification XR_EXT_dpad_binding
  company: Unity
  priority: 0
  required: 0
  forceThresholdLeft: 0.5
  forceThresholdReleaseLeft: 0.4
  centerRegionLeft: 0.5
  wedgeAngleLeft: 1.5707964
  isStickyLeft: 0
  forceThresholdRight: 0.5
  forceThresholdReleaseRight: 0.4
  centerRegionRight: 0.5
  wedgeAngleRight: 1.5707964
  isStickyRight: 0
  extensionStrings:
  - XR_KHR_binding_modification
  - XR_EXT_dpad_binding
--- !u!114 &8480006813584753048
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4710975c7840e7d499bbb3757c22b0a5, type: 3}
  m_Name: MetaXRSubsampledLayout Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Meta XR Subsampled Layout
  version: 0.0.1
  featureIdInternal: com.meta.openxr.feature.subsampledLayout
  openxrExtensionStrings: XR_META_vulkan_swapchain_create_info
  company: Meta
  priority: 0
  required: 0
--- !u!114 &8641999004521932936
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f6bfdbcb316ed242b30a8798c9eb853, type: 3}
  m_Name: KHRSimpleControllerProfile Android
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Khronos Simple Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.khrsimpleprofile
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &8676458532043711687
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: WebGL
  m_EditorClassIdentifier: 
  features:
  - {fileID: -9052863463845294752}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_spacewarpMotionVectorTextureFormat: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
  m_optimizeMultiviewRenderRegions: 0
  m_foveatedRenderingApi: 0
--- !u!114 &8805067575581680119
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a24be4b5ebfe5f4d8ed1de9b25cb7aa, type: 3}
  m_Name: HandCommonPosesInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Hand Interaction Poses
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteractionposes
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &8965433883820857904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f028123e2efe1d443875bc7609b4a98b, type: 3}
  m_Name: PalmPoseInteraction Standalone
  m_EditorClassIdentifier: 
  m_enabled: 0
  nameUi: Palm Pose
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.palmpose
  openxrExtensionStrings: XR_EXT_palm_pose
  company: Unity
  priority: 0
  required: 0
--- !u!114 &9038805937758088593
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1927c045052a06d49a9b21fdcaa26db6, type: 3}
  m_Name: MetaXRFeature Android
  m_EditorClassIdentifier: 
  m_enabled: 1
  nameUi: Meta XR Feature
  version: 0.0.1
  featureIdInternal: com.meta.openxr.feature.metaxr
  openxrExtensionStrings: 'XR_KHR_vulkan_enable XR_KHR_D3D11_enable XR_OCULUS_common_reference_spaces
    XR_FB_display_refresh_rate XR_EXT_performance_settings XR_FB_composition_layer_image_layout
    XR_KHR_android_surface_swapchain XR_FB_android_surface_swapchain_create XR_KHR_composition_layer_color_scale_bias
    XR_FB_color_space XR_EXT_hand_tracking XR_FB_swapchain_update_state XR_FB_swapchain_update_state_opengl_es
    XR_FB_swapchain_update_state_vulkan XR_FB_composition_layer_alpha_blend XR_KHR_composition_layer_depth
    XR_KHR_composition_layer_cylinder XR_KHR_composition_layer_cube XR_KHR_composition_layer_equirect2
    XR_KHR_convert_timespec_time XR_KHR_visibility_mask XR_FB_render_model XR_FB_spatial_entity
    XR_FB_spatial_entity_user XR_FB_spatial_entity_query XR_FB_spatial_entity_storage
    XR_FB_spatial_entity_storage_batch XR_META_spatial_entity_mesh XR_META_performance_metrics
    XR_FB_spatial_entity_sharing XR_FB_scene XR_FB_spatial_entity_container XR_FB_scene_capture
    XR_FB_face_tracking XR_FB_face_tracking2 XR_FB_eye_tracking XR_FB_eye_tracking_social
    XR_FB_body_tracking XR_META_body_tracking_full_body XR_META_body_tracking_calibration
    XR_META_body_tracking_fidelity XR_FB_keyboard_tracking XR_META_virtual_keyboard
    XR_FB_passthrough XR_FB_triangle_mesh XR_FB_passthrough_keyboard_hands XR_META_passthrough_layer_resumed_event
    XR_META_passthrough_color_lut XR_META_passthrough_preferences XR_OCULUS_audio_device_guid
    XR_FB_common_events XR_FB_hand_tracking_capsules XR_FB_hand_tracking_mesh XR_FB_hand_tracking_aim
    XR_FB_touch_controller_pro XR_FB_touch_controller_proximity XR_FB_composition_layer_depth_test
    XR_FB_haptic_amplitude_envelope XR_FB_haptic_pcm XR_META_local_dimming XR_META_hand_tracking_wide_motion_mode
    XR_EXT_hand_tracking_data_source XR_EXT_hand_joints_motion_range XR_META_touch_controller_plus
    XR_META_simultaneous_hands_and_controllers XR_MSFT_hand_interaction XR_EXT_hand_interaction
    XR_FB_hand_tracking_confidence XR_META_detached_controllers XR_LOGITECH_mx_ink_stylus_interaction
    XR_META_colocation_discovery XR_META_spatial_entity_sharing XR_META_spatial_entity_group_sharing
    XR_EXT_debug_utils XR_META_dynamic_object_tracker XR_META_dynamic_object_keyboard
    XR_META_hand_tracking_microgestures XR_META_spatial_entity_persistence XR_META_spatial_entity_discovery
    XR_META_boundary_visibility XR_METAX1_face_tracking_visemes XR_META_headset_id
    XR_FB_composition_layer_settings XR_META_automatic_layer_filter XR_EXT_future
    XR_META_recommended_layer_resolution XR_EXT_local_floor '
  company: Meta
  priority: 0
  required: 0
